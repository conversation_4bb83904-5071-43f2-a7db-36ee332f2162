# Import the required libraries
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from tensorflow.keras.models import Sequential
from tensorflow.keras.regularizers import l2
from sklearn.model_selection import train_test_split
import pandas as pd
import numpy as np
import cv2
import os
import matplotlib.pyplot as plt
import seaborn as sns

from sklearn import metrics 
from sklearn.metrics import classification_report
from sklearn.metrics import classification_report
import time
from tqdm import tqdm

# Set random seed for Python's built-in random number generator
import random
random_seed = 42
random.seed(random_seed)

# Set random seed for NumPy
np.random.seed(random_seed)

# Set random seed for TensorFlow
tf.random.set_seed(random_seed)

# Check for configuration of available GPU devices
from tensorflow.python.client import  device_lib
device_lib.list_local_devices()

DATADIR = '/home/<USER>/IndiCon/data/cwt_images'

# Automatically gets all 30 class folders
CATEGORIES = os.listdir(DATADIR)
CATEGORIES.sort()  # consistent order

img_size = (224, 224)



# storage for each axis
X_x, X_y, X_z, Y = [], [], [], []

for category in tqdm(CATEGORIES):
    path_x = os.path.join(DATADIR, category, "acc_x")
    path_y = os.path.join(DATADIR, category, "acc_y")
    path_z = os.path.join(DATADIR, category, "acc_z")

    class_num = CATEGORIES.index(category)

    # ensure files are aligned
    files_x = sorted(os.listdir(path_x))
    files_y = sorted(os.listdir(path_y))
    files_z = sorted(os.listdir(path_z))

    for fx, fy, fz in zip(files_x, files_y, files_z):
        try:
            # read acc_x
            img_x = cv2.imread(os.path.join(path_x, fx), cv2.IMREAD_COLOR)
            img_x = cv2.cvtColor(img_x, cv2.COLOR_BGR2RGB)
            img_x = cv2.resize(img_x, img_size)

            # read acc_y
            img_y = cv2.imread(os.path.join(path_y, fy), cv2.IMREAD_COLOR)
            img_y = cv2.cvtColor(img_y, cv2.COLOR_BGR2RGB)
            img_y = cv2.resize(img_y, img_size)

            # read acc_z
            img_z = cv2.imread(os.path.join(path_z, fz), cv2.IMREAD_COLOR)
            img_z = cv2.cvtColor(img_z, cv2.COLOR_BGR2RGB)
            img_z = cv2.resize(img_z, img_size)

            # append
            X_x.append(img_x)
            X_y.append(img_y)
            X_z.append(img_z)
            Y.append(class_num)

        except Exception as e:
            pass


# X = [] #features
# Y = [] #labels

# for features, label in dataset:
# 	X.append(features)
# 	Y.append(label)

# Convert to NumPy arrays
X_x = np.array(X_x, dtype=np.uint8)
X_y = np.array(X_y, dtype=np.uint8)
X_z = np.array(X_z, dtype=np.uint8)
Y   = np.array(Y, dtype=np.uint8)

# Save all arrays in a single .npz file
np.savez_compressed('/home/<USER>/IndiCon/data/cwt_dataset.npz', 
                    X_x=X_x, X_y=X_y, X_z=X_z, Y=Y)
print("Data saved successfully!")


X_test.shape

import numpy as np
from sklearn.model_selection import train_test_split

# Load preprocessed data
data_file = '/home/<USER>/IndiCon/data/cwt_dataset.npz'
data = np.load(data_file)

X_x = data['X_x']
X_y = data['X_y']
X_z = data['X_z']
Y   = data['Y']

print("Data shapes:", X_x.shape, X_y.shape, X_z.shape, Y.shape)


# Train/test split
X_train_x, X_test_x, \
X_train_y, X_test_y, \
X_train_z, X_test_z, \
Y_train, Y_test = train_test_split(X_x, X_y, X_z, Y, test_size=0.1, random_state=42, stratify=Y)

# Convert to numpy
X_train = [np.array(X_train_x), np.array(X_train_y), np.array(X_train_z)]
X_test  = [np.array(X_test_x),  np.array(X_test_y),  np.array(X_test_z)]
Y_train = np.array(Y_train)
Y_test  = np.array(Y_test)

# Optional: normalize
# X_train = [arr/255.0 for arr in X_train]
# X_test  = [arr/255.0 for arr in X_test]

from tensorflow.keras import layers, regularizers
# ---------------------- RESNET BUILDING BLOCKS ----------------------------------------------------
def res_net_block(input_data, filters, conv_size):
    x = layers.Conv2D(filters, conv_size, activation='relu', padding='same')(input_data)
    x = layers.Conv2D(filters, conv_size, activation=None, padding='same')(x)
    x = layers.Conv2D(filters, conv_size, activation=None, padding='same')(x)
    x = layers.Add()([x, input_data])
    x = layers.Activation('relu')(x)
    
    return x
# Custom Model construction
#-----------------------------------------------------------------------------------------------------
def createModel(img_size):
    kernelSize = (8,8)
    maxpoolSize = (6,6)

    # Shared feature extractor
    def feature_extractor(inputs):
        x = layers.BatchNormalization()(inputs)
        x = layers.Conv2D(16, kernelSize, padding='same', activation='relu')(x)
        x = layers.Dropout(0.2)(x) # dropput added 
        x = layers.MaxPooling2D(pool_size=maxpoolSize)(x)

        # Residual block(s)
        num_res_net_blocks = 1
        for i in range(num_res_net_blocks):
            x = res_net_block(x, 16, 8)
        
        x = layers.Conv2D(32, kernelSize, padding='same', activation='relu')(x)
        x = layers.MaxPooling2D(pool_size=maxpoolSize)(x)
        # x = layers.Dropout(0.4)(x)
        x = layers.Conv2D(64, kernelSize, padding='same', activation='relu')(x)
        # x = layers.Dropout(0.2)(x) # dropput added 
        x = layers.MaxPooling2D(pool_size=maxpoolSize)(x)
        # x = layers.Dropout(0.2)(x)
        x = layers.Flatten()(x)
        
        return x

    # Three inputs
    input_x = tf.keras.Input(shape=(img_size[0], img_size[1], 3), name="acc_x")
    input_y = tf.keras.Input(shape=(img_size[0], img_size[1], 3), name="acc_y")
    input_z = tf.keras.Input(shape=(img_size[0], img_size[1], 3), name="acc_z")

    # Extract features separately
    feat_x = feature_extractor(input_x)
    feat_y = feature_extractor(input_y)
    feat_z = feature_extractor(input_z)

    # Concatenate features
    concat = layers.Concatenate()([feat_x, feat_y, feat_z])

    # Dense classifier
    outputs = layers.Dense(len(CATEGORIES), activation='softmax')(concat)

    # Build model
    model = tf.keras.Model(inputs=[input_x, input_y, input_z], outputs=outputs)
    model.summary()
    return model


# Early stopping

# Create the model
img_size = (224, 224)  # Update this based on your image size

model = createModel(img_size)



import tensorflow as tf
from tensorflow.keras import layers, regularizers

def res_net_block(input_data, filters, conv_size):
    x = layers.Conv2D(filters, conv_size, padding='same', use_bias=False)(input_data)
    x = layers.BatchNormalization()(x)
    x = layers.ReLU()(x)

    x = layers.Conv2D(filters, conv_size, padding='same', use_bias=False)(x)
    x = layers.BatchNormalization()(x)

    # Project input if number of channels differ
    if input_data.shape[-1] != filters:
        shortcut = layers.Conv2D(filters, (1,1), padding='same', use_bias=False)(input_data)
        shortcut = layers.BatchNormalization()(shortcut)
    else:
        shortcut = input_data

    # Residual connection
    x = layers.Add()([x, shortcut])
    x = layers.ReLU()(x)
    return x

def feature_extractor(inputs):
    # Initial convolution
    x = layers.Conv2D(32, (7,7), padding='same', use_bias=False)(inputs)
    x = layers.BatchNormalization()(x)
    x = layers.ReLU()(x)
    x = layers.MaxPooling2D(pool_size=(4,4))(x)

    # Residual blocks
    for filters in [32, 64, 128]:
        x = res_net_block(x, filters, 3)
        x = layers.MaxPooling2D(pool_size=(2,2))(x)
        x = layers.Dropout(0.3)(x)

    # Global average pooling (reduces params, avoids overfitting)
    x = layers.GlobalAveragePooling2D()(x)
    return x

def createModel(img_size, num_classes):
    # Three inputs
    input_x = tf.keras.Input(shape=(img_size[0], img_size[1], 3), name="acc_x")
    input_y = tf.keras.Input(shape=(img_size[0], img_size[1], 3), name="acc_y")
    input_z = tf.keras.Input(shape=(img_size[0], img_size[1], 3), name="acc_z")

    # Feature extraction
    feat_x = feature_extractor(input_x)
    feat_y = feature_extractor(input_y)
    feat_z = feature_extractor(input_z)

    # Concatenate
    concat = layers.Concatenate()([feat_x, feat_y, feat_z])
    concat = layers.Dropout(0.5)(concat)

    # Dense classifier
    outputs = layers.Dense(num_classes, activation='softmax',
                           kernel_regularizer=regularizers.l2(1e-4))(concat)

    # Model
    model = tf.keras.Model(inputs=[input_x, input_y, input_z], outputs=outputs)
    return model

# Usage
img_size = (224, 224)
model = createModel(img_size, len(CATEGORIES))
model.summary()

# Compile
model.compile(
    loss=tf.keras.losses.sparse_categorical_crossentropy, 
    optimizer=tf.keras.optimizers.Adam(learning_rate=1e-4), 
    metrics=['accuracy']
)

# Train
history = model.fit(
    [np.array(X_train_x), np.array(X_train_y), np.array(X_train_z)],
    np.array(Y_train), 
    epochs=100,   # more epochs with patience
    batch_size=32, 
    validation_split=0.15,
    shuffle=True,
    callbacks=[
        tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True),
        tf.keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=5)
    ]
)


from tensorflow.keras import layers, regularizers

def res_net_block(input_data, filters, conv_size):
    x = layers.Conv2D(filters, conv_size, padding='same',
                      activation='relu',
                      kernel_regularizer=regularizers.l2(1e-4))(input_data)
    x = layers.Conv2D(filters, conv_size, padding='same',
                      activation=None,
                      kernel_regularizer=regularizers.l2(1e-4))(x)
    x = layers.Conv2D(filters, conv_size, padding='same',
                      activation=None,
                      kernel_regularizer=regularizers.l2(1e-4))(x)
    x = layers.Add()([x, input_data])
    x = layers.Activation('relu')(x)
    return x


def createModel(img_size):
    kernelSize = (8,8)
    maxpoolSize = (6,6)

    def feature_extractor(inputs):
        x = layers.BatchNormalization()(inputs)
        x = layers.Conv2D(16, kernelSize, padding='same', activation='relu',
                          kernel_regularizer=regularizers.l2(1e-4))(x)
        x = layers.MaxPooling2D(pool_size=maxpoolSize)(x)

        # Residual block
        x = res_net_block(x, 16, 8)

        x = layers.Conv2D(32, kernelSize, padding='same', activation='relu',
                          kernel_regularizer=regularizers.l2(1e-4))(x)
        x = layers.MaxPooling2D(pool_size=maxpoolSize)(x)
        x = layers.Dropout(0.4)(x)

        x = layers.Conv2D(64, kernelSize, padding='same', activation='relu',
                          kernel_regularizer=regularizers.l2(1e-4))(x)
        x = layers.MaxPooling2D(pool_size=maxpoolSize)(x)
        x = layers.Dropout(0.5)(x)

        # Reduce params
        x = layers.GlobalAveragePooling2D()(x)
        return x

    # Inputs
    input_x = tf.keras.Input(shape=(img_size[0], img_size[1], 3), name="acc_x")
    input_y = tf.keras.Input(shape=(img_size[0], img_size[1], 3), name="acc_y")
    input_z = tf.keras.Input(shape=(img_size[0], img_size[1], 3), name="acc_z")

    # Features
    feat_x = feature_extractor(input_x)
    feat_y = feature_extractor(input_y)
    feat_z = feature_extractor(input_z)

    # Concatenate
    concat = layers.Concatenate()([feat_x, feat_y, feat_z])

    # Classifier
    x = layers.Dense(128, activation='relu', kernel_regularizer=regularizers.l2(1e-4))(concat)
    x = layers.Dropout(0.5)(x)
    outputs = layers.Dense(len(CATEGORIES), activation='softmax')(x)

    model = tf.keras.Model(inputs=[input_x, input_y, input_z], outputs=outputs)
    model.summary()
    return model

# Create the model
img_size = (224, 224)  # Update this based on your image size

model = createModel(img_size)



# ----------------- Model Compile -----------------
model.compile(
    loss=tf.keras.losses.sparse_categorical_crossentropy, 
    optimizer=tf.keras.optimizers.Adam(learning_rate = 0.0001), 
    metrics=['accuracy']
)

# ----------------- Model Fit / Train -----------------
history = model.fit(
    [np.array(X_train_x), np.array(X_train_y), np.array(X_train_z)],
    np.array(Y_train), 
    epochs=40, 
    shuffle=True, 
    batch_size=64, 
    validation_split=0.1
)


# Accuracy curves
plt.plot(history.history['accuracy'], label='train_accuracy')
plt.plot(history.history['val_accuracy'], label = 'validation_accuracy')
plt.xlabel('Epoch')
plt.ylabel('Accuracy')
plt.ylim([0, 1])
plt.legend(loc='lower right')
plt.savefig('/home/<USER>/IndiCon/accuracy.png',facecolor='white')
plt.show()

# Loss curves
plt.plot(history.history['loss'], label='train_loss')
plt.plot(history.history['val_loss'], label = 'validation_loss')
plt.xlabel('Epoch')
plt.ylabel('loss')
plt.ylim([0, 1])
plt.legend(loc='upper right')
plt.savefig('/home/<USER>/IndiCon/loss.png',facecolor='white')
plt.show()

# Calculate the desired metrics
test_loss, test_acc = model.evaluate(X_test, Y_test)

# Model prediction provides probabilities of the input class
# Based on the probabilities the corresponding class category is determined.
Y_te = np.array(tf.math.argmax(model.predict(X_test),1))

# Calculate the accuracy metrics
acc = metrics.accuracy_score(Y_test, Y_te)

# Classification metrics and report
classReport = classification_report(Y_test, Y_te)

# Print and save all the metrics
print("test_accuracy:", acc*100, "\n")
print("test_loss:", test_loss, "\n") 
print(classReport)

from sklearn.metrics import confusion_matrix
import seaborn as sns
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# Predict the classes
Y_pred = np.argmax(model.predict(X_test), axis=1)

# Compute the confusion matrix
con_mat = confusion_matrix(Y_test, Y_pred)

# Normalize the confusion matrix
con_mat_norm = con_mat.astype('float') / con_mat.sum(axis=1)[:, np.newaxis]

# Create a dataframe for better visualization
con_mat_df = pd.DataFrame(
    con_mat_norm,
    index=CATEGORIES,    # Your 30 class names
    columns=CATEGORIES
)

# Plotting
plt.figure(figsize=(12,10))
sns.heatmap(con_mat_df, annot=True, cmap="Blues", fmt=".2f")
plt.ylabel('True Label')
plt.xlabel('Predicted Label')
plt.title('Normalized Confusion Matrix')
plt.tight_layout()
plt.show()


model.summary()

model.save('/home/<USER>/IndiCon/madhav_model1.h5') 

