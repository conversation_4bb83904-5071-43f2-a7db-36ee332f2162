#!/usr/bin/env python3
"""
Hyperparameter Optimization for Lightweight CNN
Focus: Smooth curves, validation accuracy, training stability
"""

import tensorflow as tf
from tensorflow.keras import layers, regularizers, optimizers, callbacks
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
import itertools
import json
from datetime import datetime

class HyperparameterOptimizer:
    def __init__(self, img_size=(224, 224), num_classes=30):
        self.img_size = img_size
        self.num_classes = num_classes
        self.results = []
    
    def create_lightweight_model(self, config):
        """Create model based on hyperparameter configuration"""
        
        def feature_extractor(inputs):
            # Normalization
            x = layers.Lambda(lambda x: tf.cast(x, tf.float32) / 255.0)(inputs)
            
            # Initial conv
            x = layers.Conv2D(config['initial_filters'], (5,5), padding='same', 
                             use_bias=False, kernel_initializer='he_normal')(x)
            x = layers.BatchNormalization()(x)
            x = layers.ReLU()(x)
            x = layers.MaxPooling2D((2,2))(x)
            
            # Feature blocks
            current_filters = config['initial_filters']
            for i in range(config['num_blocks']):
                # Increase filters gradually
                current_filters = min(current_filters * config['filter_multiplier'], 128)
                
                # Efficient convolution
                x = layers.SeparableConv2D(current_filters, (3,3), padding='same',
                                         use_bias=False, kernel_initializer='he_normal')(x)
                x = layers.BatchNormalization()(x)
                x = layers.ReLU()(x)
                
                # Conditional pooling
                if i < config['num_blocks'] - 1:
                    x = layers.MaxPooling2D((2,2))(x)
                
                # Progressive dropout
                dropout_rate = config['base_dropout'] + (i * config['dropout_increment'])
                x = layers.Dropout(min(dropout_rate, 0.5))(x)
            
            # Global pooling
            x = layers.GlobalAveragePooling2D()(x)
            return x
        
        # Multi-input setup
        inputs = [
            tf.keras.Input(shape=(*self.img_size, 3), name=f"acc_{axis}")
            for axis in ['x', 'y', 'z']
        ]
        
        # Extract features
        features = [feature_extractor(inp) for inp in inputs]
        
        # Fusion
        concat = layers.Concatenate()(features)
        
        # Classifier head
        x = layers.Dense(config['dense_units'], use_bias=False, 
                        kernel_initializer='he_normal')(concat)
        x = layers.BatchNormalization()(x)
        x = layers.ReLU()(x)
        x = layers.Dropout(config['final_dropout'])(x)
        
        # Output
        outputs = layers.Dense(self.num_classes, activation='softmax',
                             kernel_regularizer=regularizers.l2(config['l2_reg']))(x)
        
        model = tf.keras.Model(inputs=inputs, outputs=outputs)
        return model
    
    def get_optimizer(self, config):
        """Get optimizer based on configuration"""
        if config['optimizer'] == 'adamw':
            return optimizers.AdamW(
                learning_rate=config['learning_rate'],
                weight_decay=config['weight_decay'],
                beta_1=0.9, beta_2=0.999
            )
        elif config['optimizer'] == 'adam':
            return optimizers.Adam(
                learning_rate=config['learning_rate'],
                beta_1=0.9, beta_2=0.999
            )
        else:  # sgd
            return optimizers.SGD(
                learning_rate=config['learning_rate'],
                momentum=0.9, nesterov=True
            )
    
    def get_callbacks(self, config):
        """Get training callbacks"""
        callback_list = []
        
        # Learning rate scheduling
        if config['lr_schedule'] == 'cosine_warmup':
            lr_scheduler = callbacks.LearningRateScheduler(
                lambda epoch: cosine_decay_with_warmup(
                    epoch, total_epochs=config['epochs'],
                    max_lr=config['learning_rate']
                ), verbose=0
            )
            callback_list.append(lr_scheduler)
        elif config['lr_schedule'] == 'plateau':
            lr_scheduler = callbacks.ReduceLROnPlateau(
                monitor='val_loss', factor=0.8, patience=6,
                min_lr=1e-6, verbose=0
            )
            callback_list.append(lr_scheduler)
        
        # Early stopping
        early_stop = callbacks.EarlyStopping(
            monitor='val_loss', patience=config['patience'],
            restore_best_weights=True, verbose=0
        )
        callback_list.append(early_stop)
        
        return callback_list
    
    def evaluate_config(self, config, X_train, Y_train, quick_run=False):
        """Evaluate a single hyperparameter configuration"""
        
        # Create and compile model
        model = self.create_lightweight_model(config)
        optimizer = self.get_optimizer(config)
        
        model.compile(
            optimizer=optimizer,
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        # Training settings
        epochs = 15 if quick_run else config['epochs']
        
        # Train
        history = model.fit(
            X_train, Y_train,
            epochs=epochs,
            batch_size=config['batch_size'],
            validation_split=0.15,
            shuffle=True,
            callbacks=self.get_callbacks({**config, 'epochs': epochs}),
            verbose=0
        )
        
        # Calculate metrics
        final_val_acc = max(history.history['val_accuracy'])
        final_train_acc = max(history.history['accuracy'])
        
        # Smoothness metrics
        val_loss_std = np.std(history.history['val_loss'][-10:])  # Last 10 epochs
        train_loss_std = np.std(history.history['loss'][-10:])
        
        # Overfitting metric
        acc_gap = final_train_acc - final_val_acc
        
        result = {
            'config': config,
            'val_accuracy': final_val_acc,
            'train_accuracy': final_train_acc,
            'accuracy_gap': acc_gap,
            'val_loss_stability': 1 / (val_loss_std + 1e-8),
            'train_loss_stability': 1 / (train_loss_std + 1e-8),
            'overall_score': final_val_acc * (1 / (val_loss_std + 1e-8)) * (1 / (acc_gap + 0.1))
        }
        
        return result, history
    
    def grid_search(self, X_train, Y_train, quick_run=True):
        """Perform grid search over hyperparameters"""
        
        # Define search space (focused on your constraints)
        search_space = {
            'learning_rate': [5e-4, 8e-4, 1e-3, 2e-3],  # Your preferred range
            'batch_size': [16, 32, 48],  # Smaller for smoothness
            'optimizer': ['adamw', 'adam'],
            'lr_schedule': ['cosine_warmup', 'plateau'],
            'initial_filters': [24, 32],
            'num_blocks': [2, 3],
            'filter_multiplier': [1.5, 2],
            'base_dropout': [0.1, 0.15],
            'dropout_increment': [0.05, 0.1],
            'final_dropout': [0.3, 0.4],
            'dense_units': [128, 192],
            'l2_reg': [1e-5, 5e-5],
            'weight_decay': [1e-4, 5e-4],
            'patience': [10, 12],
            'epochs': [40]
        }
        
        # Generate configurations (sample subset for efficiency)
        keys = list(search_space.keys())
        
        # Smart sampling: focus on most impactful parameters
        important_configs = []
        
        # Configuration 1: Stability focused
        important_configs.append({
            'learning_rate': 5e-4, 'batch_size': 32, 'optimizer': 'adamw',
            'lr_schedule': 'cosine_warmup', 'initial_filters': 32, 'num_blocks': 2,
            'filter_multiplier': 1.5, 'base_dropout': 0.1, 'dropout_increment': 0.05,
            'final_dropout': 0.3, 'dense_units': 128, 'l2_reg': 1e-5,
            'weight_decay': 1e-4, 'patience': 12, 'epochs': 40
        })
        
        # Configuration 2: Accuracy focused
        important_configs.append({
            'learning_rate': 8e-4, 'batch_size': 16, 'optimizer': 'adamw',
            'lr_schedule': 'cosine_warmup', 'initial_filters': 32, 'num_blocks': 3,
            'filter_multiplier': 2, 'base_dropout': 0.15, 'dropout_increment': 0.1,
            'final_dropout': 0.4, 'dense_units': 192, 'l2_reg': 5e-5,
            'weight_decay': 5e-4, 'patience': 10, 'epochs': 40
        })
        
        # Configuration 3: Balanced
        important_configs.append({
            'learning_rate': 1e-3, 'batch_size': 32, 'optimizer': 'adam',
            'lr_schedule': 'plateau', 'initial_filters': 24, 'num_blocks': 2,
            'filter_multiplier': 2, 'base_dropout': 0.1, 'dropout_increment': 0.1,
            'final_dropout': 0.3, 'dense_units': 128, 'l2_reg': 1e-5,
            'weight_decay': 1e-4, 'patience': 12, 'epochs': 40
        })
        
        print(f"Testing {len(important_configs)} key configurations...")
        
        best_result = None
        best_score = 0
        
        for i, config in enumerate(important_configs):
            print(f"\nTesting configuration {i+1}/{len(important_configs)}")
            print(f"LR: {config['learning_rate']}, Batch: {config['batch_size']}, "
                  f"Optimizer: {config['optimizer']}")
            
            try:
                result, history = self.evaluate_config(config, X_train, Y_train, quick_run)
                self.results.append(result)
                
                print(f"Val Acc: {result['val_accuracy']:.4f}, "
                      f"Gap: {result['accuracy_gap']:.4f}, "
                      f"Score: {result['overall_score']:.4f}")
                
                if result['overall_score'] > best_score:
                    best_score = result['overall_score']
                    best_result = result
                    
            except Exception as e:
                print(f"Configuration failed: {e}")
                continue
        
        return best_result
    
    def save_results(self, filename="hyperparameter_results.json"):
        """Save optimization results"""
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)

# Main execution function
def optimize_hyperparameters(data_path):
    """Run hyperparameter optimization"""
    
    # Load data
    print("Loading data for optimization...")
    data = np.load(data_path)
    X_x, X_y, X_z, Y = data['X_x'], data['X_y'], data['X_z'], data['Y']
    
    # Split data
    X_train_x, _, X_train_y, _, X_train_z, _, Y_train, _ = \
        train_test_split(X_x, X_y, X_z, Y, test_size=0.1, random_state=42, stratify=Y)
    
    X_train = [X_train_x, X_train_y, X_train_z]
    
    # Initialize optimizer
    optimizer = HyperparameterOptimizer(num_classes=len(np.unique(Y)))
    
    # Run optimization
    best_result = optimizer.grid_search(X_train, Y_train, quick_run=True)
    
    # Save results
    optimizer.save_results()
    
    print("\n" + "="*60)
    print("OPTIMIZATION COMPLETE")
    print("="*60)
    print(f"Best validation accuracy: {best_result['val_accuracy']:.4f}")
    print(f"Training-validation gap: {best_result['accuracy_gap']:.4f}")
    print(f"Overall score: {best_result['overall_score']:.4f}")
    print("\nBest configuration:")
    for key, value in best_result['config'].items():
        print(f"  {key}: {value}")
    
    return best_result

if __name__ == "__main__":
    # Update path to your data
    DATA_PATH = "data/cwt_dataset.npz"
    
    if os.path.exists(DATA_PATH):
        best_config = optimize_hyperparameters(DATA_PATH)
    else:
        print("Please update DATA_PATH to point to your dataset file")
