#!/usr/bin/env python3
"""
Improved CNN Training Script for Lightweight Models
Addresses: overfitting, smooth curves, validation accuracy, training stability
"""

import tensorflow as tf
from tensorflow.keras import layers, regularizers, optimizers, callbacks
import numpy as np
import matplotlib.pyplot as plt
import os
from sklearn.model_selection import train_test_split
import math

# Configuration
class TrainingConfig:
    # Data settings
    IMG_SIZE = (224, 224)
    BATCH_SIZE = 32  # Smaller for smoother gradients
    VALIDATION_SPLIT = 0.15
    
    # Training settings
    EPOCHS = 60
    INITIAL_LR = 8e-4  # In your preferred range
    MIN_LR = 1e-5
    
    # Regularization
    DROPOUT_RATES = [0.1, 0.2, 0.3]  # Progressive
    L2_REG = 1e-5  # Lighter regularization
    LABEL_SMOOTHING = 0.05  # Light smoothing
    
    # Callbacks
    EARLY_STOPPING_PATIENCE = 12
    LR_PATIENCE = 6

def cosine_decay_with_warmup(epoch, warmup_epochs=5, total_epochs=60, 
                           max_lr=8e-4, min_lr=1e-5):
    """Cosine decay with warmup for smooth training"""
    if epoch < warmup_epochs:
        return max_lr * epoch / warmup_epochs
    else:
        progress = (epoch - warmup_epochs) / (total_epochs - warmup_epochs)
        return min_lr + (max_lr - min_lr) * 0.5 * (1 + math.cos(math.pi * progress))

def create_improved_model(img_size, num_classes):
    """Create lightweight CNN with improved architecture"""
    
    def efficient_block(x, filters, kernel_size=3, strides=1):
        """Efficient convolution block"""
        # Depthwise separable convolution for efficiency
        x = layers.SeparableConv2D(filters, kernel_size, strides=strides, 
                                 padding='same', use_bias=False,
                                 kernel_initializer='he_normal')(x)
        x = layers.BatchNormalization()(x)
        x = layers.ReLU()(x)
        return x
    
    def feature_extractor(inputs, name_prefix=""):
        """Lightweight feature extractor"""
        # Input normalization
        x = layers.Lambda(lambda x: tf.cast(x, tf.float32) / 255.0)(inputs)
        
        # Initial conv
        x = layers.Conv2D(32, (5,5), padding='same', use_bias=False,
                         kernel_initializer='he_normal')(x)
        x = layers.BatchNormalization()(x)
        x = layers.ReLU()(x)
        x = layers.MaxPooling2D((2,2))(x)
        
        # Efficient blocks with progressive complexity
        filters = [32, 64, 96]
        for i, f in enumerate(filters):
            x = efficient_block(x, f, kernel_size=3)
            
            if i < len(filters) - 1:
                x = layers.MaxPooling2D((2,2))(x)
            
            # Progressive dropout
            x = layers.Dropout(TrainingConfig.DROPOUT_RATES[i])(x)
        
        # Global pooling to reduce parameters
        x = layers.GlobalAveragePooling2D()(x)
        return x
    
    # Multi-input architecture
    input_x = tf.keras.Input(shape=(*img_size, 3), name="acc_x")
    input_y = tf.keras.Input(shape=(*img_size, 3), name="acc_y") 
    input_z = tf.keras.Input(shape=(*img_size, 3), name="acc_z")
    
    # Feature extraction
    feat_x = feature_extractor(input_x, "x")
    feat_y = feature_extractor(input_y, "y")
    feat_z = feature_extractor(input_z, "z")
    
    # Feature fusion
    concat = layers.Concatenate()([feat_x, feat_y, feat_z])
    
    # Lightweight classifier
    x = layers.Dense(192, use_bias=False, kernel_initializer='he_normal')(concat)
    x = layers.BatchNormalization()(x)
    x = layers.ReLU()(x)
    x = layers.Dropout(0.4)(x)
    
    x = layers.Dense(96, use_bias=False, kernel_initializer='he_normal')(x)
    x = layers.BatchNormalization()(x)
    x = layers.ReLU()(x)
    x = layers.Dropout(0.3)(x)
    
    # Output with light regularization
    outputs = layers.Dense(num_classes, activation='softmax',
                          kernel_regularizer=regularizers.l2(TrainingConfig.L2_REG))(x)
    
    model = tf.keras.Model(inputs=[input_x, input_y, input_z], outputs=outputs)
    return model

def get_improved_callbacks():
    """Get callbacks for stable training"""
    return [
        callbacks.LearningRateScheduler(cosine_decay_with_warmup, verbose=1),
        callbacks.EarlyStopping(
            monitor='val_loss',
            patience=TrainingConfig.EARLY_STOPPING_PATIENCE,
            restore_best_weights=True,
            verbose=1
        ),
        callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.8,
            patience=TrainingConfig.LR_PATIENCE,
            min_lr=TrainingConfig.MIN_LR,
            verbose=1
        )
    ]

def train_with_improved_settings(data_path):
    """Main training function"""
    
    # Load data
    print("Loading data...")
    data = np.load(data_path)
    X_x, X_y, X_z, Y = data['X_x'], data['X_y'], data['X_z'], data['Y']
    
    # Split data
    X_train_x, X_test_x, X_train_y, X_test_y, X_train_z, X_test_z, Y_train, Y_test = \
        train_test_split(X_x, X_y, X_z, Y, test_size=0.1, random_state=42, stratify=Y)
    
    X_train = [X_train_x, X_train_y, X_train_z]
    X_test = [X_test_x, X_test_y, X_test_z]
    
    print(f"Training data shape: {[x.shape for x in X_train]}")
    print(f"Number of classes: {len(np.unique(Y))}")
    
    # Create model
    model = create_improved_model(TrainingConfig.IMG_SIZE, len(np.unique(Y)))
    
    # Compile with AdamW for stability
    model.compile(
        optimizer=optimizers.AdamW(
            learning_rate=TrainingConfig.INITIAL_LR,
            weight_decay=1e-4
        ),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    print("\nModel Summary:")
    model.summary()
    
    # Train
    print("\nStarting training...")
    history = model.fit(
        X_train, Y_train,
        epochs=TrainingConfig.EPOCHS,
        batch_size=TrainingConfig.BATCH_SIZE,
        validation_split=TrainingConfig.VALIDATION_SPLIT,
        shuffle=True,
        callbacks=get_improved_callbacks(),
        verbose=1
    )
    
    # Evaluate
    test_loss, test_acc = model.evaluate(X_test, Y_test, verbose=0)
    print(f"\nFinal Results:")
    print(f"Test Accuracy: {test_acc:.4f}")
    print(f"Test Loss: {test_loss:.4f}")
    
    # Plot results
    plot_smooth_curves(history)
    
    return model, history

def plot_smooth_curves(history):
    """Plot training curves with smoothing"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Smooth curves using moving average
    def smooth_curve(points, factor=0.9):
        smoothed_points = []
        for point in points:
            if smoothed_points:
                previous = smoothed_points[-1]
                smoothed_points.append(previous * factor + point * (1 - factor))
            else:
                smoothed_points.append(point)
        return smoothed_points
    
    # Raw accuracy
    axes[0,0].plot(history.history['accuracy'], alpha=0.3, label='Raw Training')
    axes[0,0].plot(history.history['val_accuracy'], alpha=0.3, label='Raw Validation')
    axes[0,0].plot(smooth_curve(history.history['accuracy']), label='Smooth Training')
    axes[0,0].plot(smooth_curve(history.history['val_accuracy']), label='Smooth Validation')
    axes[0,0].set_title('Accuracy Curves')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # Raw loss
    axes[0,1].plot(history.history['loss'], alpha=0.3, label='Raw Training')
    axes[0,1].plot(history.history['val_loss'], alpha=0.3, label='Raw Validation')
    axes[0,1].plot(smooth_curve(history.history['loss']), label='Smooth Training')
    axes[0,1].plot(smooth_curve(history.history['val_loss']), label='Smooth Validation')
    axes[0,1].set_title('Loss Curves')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    
    # Learning rate
    if 'lr' in history.history:
        axes[1,0].plot(history.history['lr'])
        axes[1,0].set_title('Learning Rate Schedule')
        axes[1,0].set_yscale('log')
        axes[1,0].grid(True, alpha=0.3)
    
    # Accuracy gap analysis
    train_acc = np.array(history.history['accuracy'])
    val_acc = np.array(history.history['val_accuracy'])
    gap = train_acc - val_acc
    axes[1,1].plot(gap, label='Accuracy Gap')
    axes[1,1].axhline(y=0, color='r', linestyle='--', alpha=0.5)
    axes[1,1].set_title('Training-Validation Gap')
    axes[1,1].legend()
    axes[1,1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('improved_training_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    # Update this path to your data file
    DATA_PATH = "data/cwt_dataset.npz"  # Adjust path as needed
    
    if os.path.exists(DATA_PATH):
        model, history = train_with_improved_settings(DATA_PATH)
    else:
        print(f"Data file not found at {DATA_PATH}")
        print("Please update the DATA_PATH variable to point to your cwt_dataset.npz file")
