#!/usr/bin/env python3
"""
Complete Solution for Stable Lightweight CNN Training
Addresses all your specific concerns:
- Smooth training curves
- Better validation accuracy 
- Reduced overfitting
- Learning rate in 1e-3 to 1e-2 range
- No data augmentation
"""

import tensorflow as tf
from tensorflow.keras import layers, regularizers, optimizers, callbacks
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
import math

# Set seeds for reproducibility
tf.random.set_seed(42)
np.random.seed(42)

class StableCNNSolution:
    def __init__(self, img_size=(224, 224), num_classes=30):
        self.img_size = img_size
        self.num_classes = num_classes
    
    def cosine_warmup_schedule(self, epoch, warmup_epochs=8, total_epochs=50, 
                              max_lr=8e-4, min_lr=5e-5):
        """Smooth learning rate schedule with warmup"""
        if epoch < warmup_epochs:
            # Linear warmup
            return max_lr * epoch / warmup_epochs
        else:
            # Cosine annealing
            progress = (epoch - warmup_epochs) / (total_epochs - warmup_epochs)
            return min_lr + (max_lr - min_lr) * 0.5 * (1 + math.cos(math.pi * progress))
    
    def create_stable_model(self):
        """Create optimized lightweight model"""
        
        def stable_feature_extractor(inputs):
            # Input preprocessing and normalization
            x = layers.Lambda(lambda x: tf.cast(x, tf.float32) / 255.0)(inputs)
            
            # Initial feature extraction
            x = layers.Conv2D(32, (5,5), padding='same', use_bias=False,
                             kernel_initializer='he_normal')(x)
            x = layers.BatchNormalization(momentum=0.99)(x)  # Stable BN
            x = layers.ReLU()(x)
            x = layers.MaxPooling2D((2,2))(x)
            
            # Efficient feature blocks
            for i, filters in enumerate([48, 64, 96]):
                # Depthwise separable conv for efficiency
                x = layers.SeparableConv2D(filters, (3,3), padding='same',
                                         use_bias=False, kernel_initializer='he_normal')(x)
                x = layers.BatchNormalization(momentum=0.99)(x)
                x = layers.ReLU()(x)
                
                # Conditional pooling
                if i < 2:  # Don't pool after last block
                    x = layers.MaxPooling2D((2,2))(x)
                
                # Light, progressive dropout
                dropout_rate = 0.1 + (i * 0.05)  # 0.1, 0.15, 0.2
                x = layers.Dropout(dropout_rate)(x)
            
            # Global average pooling
            x = layers.GlobalAveragePooling2D()(x)
            return x
        
        # Multi-input architecture
        input_x = tf.keras.Input(shape=(*self.img_size, 3), name="acc_x")
        input_y = tf.keras.Input(shape=(*self.img_size, 3), name="acc_y")
        input_z = tf.keras.Input(shape=(*self.img_size, 3), name="acc_z")
        
        # Feature extraction
        feat_x = stable_feature_extractor(input_x)
        feat_y = stable_feature_extractor(input_y)
        feat_z = stable_feature_extractor(input_z)
        
        # Feature fusion
        concat = layers.Concatenate()([feat_x, feat_y, feat_z])
        
        # Stable classifier head
        x = layers.Dense(128, use_bias=False, kernel_initializer='he_normal')(concat)
        x = layers.BatchNormalization(momentum=0.99)(x)
        x = layers.ReLU()(x)
        x = layers.Dropout(0.3)(x)
        
        # Output layer
        outputs = layers.Dense(self.num_classes, activation='softmax',
                             kernel_regularizer=regularizers.l2(1e-5))(x)
        
        model = tf.keras.Model(inputs=[input_x, input_y, input_z], outputs=outputs)
        return model
    
    def get_stable_callbacks(self, total_epochs=50):
        """Get callbacks optimized for stability"""
        return [
            # Smooth learning rate schedule
            callbacks.LearningRateScheduler(
                lambda epoch: self.cosine_warmup_schedule(epoch, total_epochs=total_epochs),
                verbose=1
            ),
            
            # Conservative early stopping
            callbacks.EarlyStopping(
                monitor='val_loss',
                patience=15,  # More patience for stability
                restore_best_weights=True,
                verbose=1
            ),
            
            # Backup LR reduction
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.8,
                patience=8,
                min_lr=1e-6,
                verbose=1
            )
        ]
    
    def train_stable_model(self, X_train, Y_train, X_test=None, Y_test=None):
        """Complete training pipeline for stable results"""
        
        print("Creating stable lightweight CNN model...")
        model = self.create_stable_model()
        
        # Compile with AdamW for stability
        model.compile(
            optimizer=optimizers.AdamW(
                learning_rate=8e-4,  # In your preferred range
                weight_decay=1e-4,   # Built-in regularization
                beta_1=0.9,
                beta_2=0.999
            ),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        print("\nModel Summary:")
        model.summary()
        
        # Calculate model size
        total_params = model.count_params()
        print(f"\nModel size: {total_params:,} parameters")
        
        # Training
        print("\nStarting stable training...")
        history = model.fit(
            X_train, Y_train,
            epochs=50,
            batch_size=32,  # Optimal for smoothness
            validation_split=0.15,
            shuffle=True,
            callbacks=self.get_stable_callbacks(total_epochs=50),
            verbose=1
        )
        
        # Evaluation
        if X_test is not None and Y_test is not None:
            test_loss, test_acc = model.evaluate(X_test, Y_test, verbose=0)
            print(f"\nFinal Test Results:")
            print(f"Test Accuracy: {test_acc:.4f}")
            print(f"Test Loss: {test_loss:.4f}")
        
        # Analysis
        self.analyze_training_stability(history)
        
        return model, history
    
    def analyze_training_stability(self, history):
        """Analyze training stability and provide insights"""
        
        train_acc = np.array(history.history['accuracy'])
        val_acc = np.array(history.history['val_accuracy'])
        train_loss = np.array(history.history['loss'])
        val_loss = np.array(history.history['val_loss'])
        
        # Calculate metrics
        final_gap = train_acc[-1] - val_acc[-1]
        max_val_acc = np.max(val_acc)
        loss_crossover_epochs = np.where(train_loss > val_loss)[0]
        
        # Smoothness analysis
        val_acc_smoothness = 1 / (np.std(np.diff(val_acc[-15:])) + 1e-8)
        train_loss_smoothness = 1 / (np.std(np.diff(train_loss[-15:])) + 1e-8)
        
        print(f"\n{'='*50}")
        print("TRAINING STABILITY ANALYSIS")
        print(f"{'='*50}")
        print(f"Maximum validation accuracy: {max_val_acc:.4f}")
        print(f"Final accuracy gap: {final_gap:.4f}")
        print(f"Validation accuracy smoothness: {val_acc_smoothness:.2f}")
        print(f"Training loss smoothness: {train_loss_smoothness:.2f}")
        
        if len(loss_crossover_epochs) > 0:
            crossover_pct = len(loss_crossover_epochs) / len(train_loss) * 100
            print(f"Training loss > validation loss: {crossover_pct:.1f}% of epochs")
        else:
            print("Training loss consistently below validation loss ✓")
        
        # Plot comprehensive analysis
        self.plot_comprehensive_analysis(history)
    
    def plot_comprehensive_analysis(self, history):
        """Create comprehensive training analysis plots"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        
        # Smooth the curves for better visualization
        def smooth(data, alpha=0.9):
            smoothed = []
            for point in data:
                if smoothed:
                    smoothed.append(smoothed[-1] * alpha + point * (1 - alpha))
                else:
                    smoothed.append(point)
            return smoothed
        
        # 1. Accuracy curves
        axes[0,0].plot(history.history['accuracy'], alpha=0.3, label='Raw Training')
        axes[0,0].plot(history.history['val_accuracy'], alpha=0.3, label='Raw Validation')
        axes[0,0].plot(smooth(history.history['accuracy']), label='Smooth Training', linewidth=2)
        axes[0,0].plot(smooth(history.history['val_accuracy']), label='Smooth Validation', linewidth=2)
        axes[0,0].set_title('Accuracy Curves')
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        
        # 2. Loss curves
        axes[0,1].plot(history.history['loss'], alpha=0.3, label='Raw Training')
        axes[0,1].plot(history.history['val_loss'], alpha=0.3, label='Raw Validation')
        axes[0,1].plot(smooth(history.history['loss']), label='Smooth Training', linewidth=2)
        axes[0,1].plot(smooth(history.history['val_loss']), label='Smooth Validation', linewidth=2)
        axes[0,1].set_title('Loss Curves')
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)
        
        # 3. Learning rate
        if 'lr' in history.history:
            axes[0,2].plot(history.history['lr'], linewidth=2)
            axes[0,2].set_title('Learning Rate Schedule')
            axes[0,2].set_yscale('log')
            axes[0,2].grid(True, alpha=0.3)
        
        # 4. Accuracy gap
        train_acc = np.array(history.history['accuracy'])
        val_acc = np.array(history.history['val_accuracy'])
        gap = train_acc - val_acc
        axes[1,0].plot(gap, linewidth=2, color='red')
        axes[1,0].axhline(y=0, color='black', linestyle='--', alpha=0.5)
        axes[1,0].set_title('Training-Validation Accuracy Gap')
        axes[1,0].grid(True, alpha=0.3)
        
        # 5. Loss difference
        train_loss = np.array(history.history['loss'])
        val_loss = np.array(history.history['val_loss'])
        loss_diff = train_loss - val_loss
        axes[1,1].plot(loss_diff, linewidth=2, color='orange')
        axes[1,1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
        axes[1,1].set_title('Training-Validation Loss Difference')
        axes[1,1].grid(True, alpha=0.3)
        
        # 6. Validation accuracy trend
        val_acc_trend = np.convolve(val_acc, np.ones(5)/5, mode='valid')  # 5-epoch moving average
        axes[1,2].plot(range(len(val_acc_trend)), val_acc_trend, linewidth=2, color='green')
        axes[1,2].set_title('Validation Accuracy Trend (5-epoch MA)')
        axes[1,2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('comprehensive_training_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

# Quick start function for your existing data
def quick_improved_training(data_path):
    """Quick start with improved settings"""
    
    # Load your data
    data = np.load(data_path)
    X_x, X_y, X_z, Y = data['X_x'], data['X_y'], data['X_z'], data['Y']
    
    # Split
    X_train_x, X_test_x, X_train_y, X_test_y, X_train_z, X_test_z, Y_train, Y_test = \
        train_test_split(X_x, X_y, X_z, Y, test_size=0.1, random_state=42, stratify=Y)
    
    X_train = [X_train_x, X_train_y, X_train_z]
    X_test = [X_test_x, X_test_y, X_test_z]
    
    # Initialize solution
    solution = StableCNNSolution(num_classes=len(np.unique(Y)))
    
    # Train
    model, history = solution.train_stable_model(X_train, Y_train, X_test, Y_test)
    
    return model, history

if __name__ == "__main__":
    # Update this path to your data file
    DATA_PATH = "data/cwt_dataset.npz"
    
    print("🚀 Starting Stable CNN Training Solution")
    print("="*60)
    
    if os.path.exists(DATA_PATH):
        model, history = quick_improved_training(DATA_PATH)
        print("\n✅ Training completed successfully!")
    else:
        print(f"❌ Data file not found: {DATA_PATH}")
        print("Please update DATA_PATH to point to your cwt_dataset.npz file")
        
        # Show example of how to use with your existing code
        print("\n📝 To use with your existing data loading code:")
        print("""
# After loading your data:
X_train = [X_train_x, X_train_y, X_train_z]
X_test = [X_test_x, X_test_y, X_test_z]

solution = StableCNNSolution(num_classes=len(CATEGORIES))
model, history = solution.train_stable_model(X_train, Y_train, X_test, Y_test)
        """)
